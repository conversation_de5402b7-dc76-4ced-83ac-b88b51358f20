"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateUserSubscription = exports.getUserSubscription = exports.verifyAuthToken = void 0;
const admin = __importStar(require("firebase-admin"));
// Helper function to verify Firebase auth token
const verifyAuthToken = async (req) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return null;
        }
        const token = authHeader.split('Bearer ')[1];
        const decodedToken = await admin.auth().verifyIdToken(token);
        return decodedToken;
    }
    catch (error) {
        console.error('Error verifying auth token:', error);
        return null;
    }
};
exports.verifyAuthToken = verifyAuthToken;
// Helper function to get user subscription
const getUserSubscription = async (userId) => {
    try {
        const db = admin.firestore();
        const subscriptionDoc = await db.collection('subscriptions').doc(userId).get();
        return subscriptionDoc.exists ? subscriptionDoc.data() : null;
    }
    catch (error) {
        console.error('Error getting user subscription:', error);
        return null;
    }
};
exports.getUserSubscription = getUserSubscription;
// Helper function to update user subscription
const updateUserSubscription = async (userId, data) => {
    try {
        const db = admin.firestore();
        await db.collection('subscriptions').doc(userId).set(Object.assign(Object.assign({}, data), { updatedAt: admin.firestore.FieldValue.serverTimestamp() }), { merge: true });
    }
    catch (error) {
        console.error('Error updating user subscription:', error);
        throw error;
    }
};
exports.updateUserSubscription = updateUserSubscription;
//# sourceMappingURL=auth.js.map