{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/utils/auth.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sDAAwC;AAGxC,gDAAgD;AACzC,MAAM,eAAe,GAAG,KAAK,EAAE,GAAoB,EAA6C,EAAE;IACvG,IAAI;QACF,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;YACpD,OAAO,IAAI,CAAC;SACb;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC7D,OAAO,YAAY,CAAC;KACrB;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC;KACb;AACH,CAAC,CAAC;AAdW,QAAA,eAAe,mBAc1B;AAEF,2CAA2C;AACpC,MAAM,mBAAmB,GAAG,KAAK,EAAE,MAAc,EAAE,EAAE;IAC1D,IAAI;QACF,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC7B,MAAM,eAAe,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;QAC/E,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;KAC/D;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC;KACb;AACH,CAAC,CAAC;AATW,QAAA,mBAAmB,uBAS9B;AAEF,8CAA8C;AACvC,MAAM,sBAAsB,GAAG,KAAK,EAAE,MAAc,EAAE,IAAS,EAAE,EAAE;IACxE,IAAI;QACF,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC7B,MAAM,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,iCAC/C,IAAI,KACP,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE,KACtD,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;KACrB;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,MAAM,KAAK,CAAC;KACb;AACH,CAAC,CAAC;AAXW,QAAA,sBAAsB,0BAWjC"}