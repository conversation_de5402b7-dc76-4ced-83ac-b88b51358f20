"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyOrder = exports.createOrder = exports.cancelRazorpaySubscription = exports.verifyPayment = exports.createSubscription = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
const razorpay_1 = __importDefault(require("razorpay"));
const crypto_1 = __importDefault(require("crypto"));
const auth_1 = require("../utils/auth");
const db = admin.firestore();
// Get Razorpay credentials from config or env
const getRazorpayKeyId = () => {
    var _a;
    try {
        return ((_a = functions.config().razorpay) === null || _a === void 0 ? void 0 : _a.key_id) || process.env.RAZORPAY_KEY_ID || '';
    }
    catch (_b) {
        return process.env.RAZORPAY_KEY_ID || '';
    }
};
const getRazorpayKeySecret = () => {
    var _a;
    try {
        return ((_a = functions.config().razorpay) === null || _a === void 0 ? void 0 : _a.key_secret) || process.env.RAZORPAY_KEY_SECRET || '';
    }
    catch (_b) {
        return process.env.RAZORPAY_KEY_SECRET || '';
    }
};
const RAZORPAY_KEY_ID = getRazorpayKeyId();
const RAZORPAY_KEY_SECRET = getRazorpayKeySecret();
const razorpay = new razorpay_1.default({ key_id: RAZORPAY_KEY_ID, key_secret: RAZORPAY_KEY_SECRET });
const createSubscription = async (req, res) => {
    try {
        if (!RAZORPAY_KEY_ID || !RAZORPAY_KEY_SECRET) {
            console.error('Razorpay keys are not configured');
            return res.status(500).json({ error: 'Razorpay is not configured' });
        }
        // Verify authentication
        const user = await (0, auth_1.verifyAuthToken)(req);
        if (!user)
            return res.status(401).json({ error: 'Unauthorized' });
        const { planId, totalCount, quantity, customerNotify, notes } = req.body;
        if (!planId)
            return res.status(400).json({ error: 'Missing planId' });
        // Create subscription on Razorpay
        const subscription = await razorpay.subscriptions.create({
            plan_id: planId,
            // Keep total_count reasonable to avoid exceeding Razorpay's end_time limit
            total_count: totalCount !== null && totalCount !== void 0 ? totalCount : 60,
            quantity: quantity !== null && quantity !== void 0 ? quantity : 1,
            customer_notify: customerNotify !== null && customerNotify !== void 0 ? customerNotify : 1,
            notes: Object.assign({ firebaseUserId: user.uid }, (notes || {})),
        });
        // Persist mapping for quick lookup (omit undefined fields)
        const userUpdate = {
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        };
        if (subscription && subscription.customer_id) {
            userUpdate.razorpayCustomerId = subscription.customer_id;
        }
        await db.collection('users').doc(user.uid).set(userUpdate, { merge: true });
        return res.json({
            subscriptionId: subscription.id,
            razorpayKey: RAZORPAY_KEY_ID,
            customerId: subscription.customer_id,
            status: subscription.status,
        });
    }
    catch (error) {
        console.error('Error creating Razorpay subscription:', error);
        return res.status(500).json({ error: 'Internal server error' });
    }
};
exports.createSubscription = createSubscription;
// Verify payment/subscription signature from Checkout handler
const verifyPayment = async (req, res) => {
    try {
        // Verify authentication
        const user = await (0, auth_1.verifyAuthToken)(req);
        if (!user)
            return res.status(401).json({ error: 'Unauthorized' });
        const { razorpay_payment_id, razorpay_subscription_id, razorpay_signature } = req.body;
        if (!razorpay_payment_id || !razorpay_subscription_id || !razorpay_signature) {
            return res.status(400).json({ error: 'Missing required fields' });
        }
        const hmac = crypto_1.default.createHmac('sha256', RAZORPAY_KEY_SECRET);
        hmac.update(`${razorpay_payment_id}|${razorpay_subscription_id}`);
        const expectedSignature = hmac.digest('hex');
        const isValid = expectedSignature === razorpay_signature;
        if (!isValid) {
            console.error('Invalid Razorpay signature');
            return res.status(400).json({ error: 'Invalid signature' });
        }
        // Mark subscription active for the user
        const subscriptionId = razorpay_subscription_id;
        await db.collection('users').doc(user.uid).collection('subscriptions').doc('current').set({
            id: 'current',
            userId: user.uid,
            planType: 'pro',
            status: 'active',
            razorpaySubscriptionId: subscriptionId,
            currentPeriodStart: admin.firestore.FieldValue.serverTimestamp(),
            currentPeriodEnd: admin.firestore.FieldValue.serverTimestamp(),
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        }, { merge: true });
        return res.json({ success: true });
    }
    catch (error) {
        console.error('Error verifying Razorpay payment:', error);
        return res.status(500).json({ error: 'Internal server error' });
    }
};
exports.verifyPayment = verifyPayment;
const cancelRazorpaySubscription = async (req, res) => {
    try {
        // Verify authentication
        const user = await (0, auth_1.verifyAuthToken)(req);
        if (!user)
            return res.status(401).json({ error: 'Unauthorized' });
        // Get user's subscription
        const subDoc = await db.collection('users').doc(user.uid).collection('subscriptions').doc('current').get();
        const sub = subDoc.data();
        if (!(sub === null || sub === void 0 ? void 0 : sub.razorpaySubscriptionId)) {
            return res.status(400).json({ error: 'No active subscription found' });
        }
        await razorpay.subscriptions.cancel(sub.razorpaySubscriptionId, { cancel_at_cycle_end: 1 });
        await db.collection('users').doc(user.uid).collection('subscriptions').doc('current').set({
            status: 'cancelled',
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        }, { merge: true });
        return res.json({ success: true, message: 'Subscription will be cancelled at period end' });
    }
    catch (error) {
        console.error('Error cancelling Razorpay subscription:', error);
        return res.status(500).json({ error: 'Internal server error' });
    }
};
exports.cancelRazorpaySubscription = cancelRazorpaySubscription;
// One-time payments (Orders API)
const createOrder = async (req, res) => {
    try {
        // Verify authentication
        const user = await (0, auth_1.verifyAuthToken)(req);
        if (!user)
            return res.status(401).json({ error: 'Unauthorized' });
        const { amount, currency } = (req.body || {});
        if (!amount)
            return res.status(400).json({ error: 'Missing amount' });
        const order = await razorpay.orders.create({
            amount: Math.round(amount * 100),
            currency: currency || 'INR',
            receipt: `rcp_${Date.now()}`,
            notes: { firebaseUserId: user.uid },
        });
        return res.json({ id: order.id, amount: order.amount, currency: order.currency, razorpayKey: RAZORPAY_KEY_ID });
    }
    catch (error) {
        console.error('Error creating Razorpay order:', error);
        return res.status(500).json({ error: 'Internal server error' });
    }
};
exports.createOrder = createOrder;
const verifyOrder = async (req, res) => {
    try {
        // Verify authentication
        const user = await (0, auth_1.verifyAuthToken)(req);
        if (!user)
            return res.status(401).json({ error: 'Unauthorized' });
        const { razorpay_payment_id, razorpay_order_id, razorpay_signature } = req.body;
        if (!razorpay_payment_id || !razorpay_order_id || !razorpay_signature) {
            return res.status(400).json({ error: 'Missing required fields' });
        }
        const hmac = crypto_1.default.createHmac('sha256', RAZORPAY_KEY_SECRET);
        hmac.update(`${razorpay_order_id}|${razorpay_payment_id}`);
        const expectedSignature = hmac.digest('hex');
        if (expectedSignature !== razorpay_signature) {
            return res.status(400).json({ error: 'Invalid signature' });
        }
        // Optionally record a one-time purchase record under the user
        await db.collection('users').doc(user.uid).collection('payments').doc(razorpay_order_id).set({
            id: razorpay_order_id,
            paymentId: razorpay_payment_id,
            amount: admin.firestore.FieldValue.increment(0),
            status: 'paid',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
        }, { merge: true });
        return res.json({ success: true });
    }
    catch (error) {
        console.error('Error verifying Razorpay order:', error);
        return res.status(500).json({ error: 'Internal server error' });
    }
};
exports.verifyOrder = verifyOrder;
//# sourceMappingURL=razorpay.js.map