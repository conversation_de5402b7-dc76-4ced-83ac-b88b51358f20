"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.trackDocumentCreation = exports.trackAIUsage = void 0;
const admin = __importStar(require("firebase-admin"));
const auth_1 = require("../utils/auth");
const db = admin.firestore();
const trackAIUsage = async (req, res) => {
    try {
        // Verify authentication
        const user = await (0, auth_1.verifyAuthToken)(req);
        if (!user) {
            return res.status(401).json({ error: 'Unauthorized' });
        }
        const userId = user.uid;
        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
        const usageDocId = `${userId}_${currentMonth}`;
        // Get or create usage document
        const usageRef = db.collection('users').doc(userId).collection('usage').doc(currentMonth);
        const usageDoc = await usageRef.get();
        if (usageDoc.exists) {
            // Increment AI usage count
            await usageRef.update({
                aiUsageCount: admin.firestore.FieldValue.increment(1),
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
            });
        }
        else {
            // Create new usage document
            await usageRef.set({
                userId: userId,
                monthYear: currentMonth,
                aiUsageCount: 1,
                documentsCreated: 0,
                speechToTextMinutes: 0,
                createdAt: admin.firestore.FieldValue.serverTimestamp(),
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
            });
        }
        // Get updated usage data
        const updatedUsageDoc = await usageRef.get();
        const updatedUsage = updatedUsageDoc.data();
        // Get user's subscription to determine limits
        const subscriptionDoc = await db.collection('users').doc(userId).collection('subscriptions').doc('current').get();
        const subscription = subscriptionDoc.data();
        const planType = (subscription === null || subscription === void 0 ? void 0 : subscription.planType) || 'free';
        const aiLimit = planType === 'pro' ? 500 : 10;
        const remaining = Math.max(0, aiLimit - ((updatedUsage === null || updatedUsage === void 0 ? void 0 : updatedUsage.aiUsageCount) || 0));
        res.json({
            success: true,
            remaining: remaining,
            usage: (updatedUsage === null || updatedUsage === void 0 ? void 0 : updatedUsage.aiUsageCount) || 0,
            limit: aiLimit
        });
    }
    catch (error) {
        console.error('Error tracking AI usage:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};
exports.trackAIUsage = trackAIUsage;
const trackDocumentCreation = async (req, res) => {
    try {
        // Verify authentication
        const user = await (0, auth_1.verifyAuthToken)(req);
        if (!user) {
            return res.status(401).json({ error: 'Unauthorized' });
        }
        const userId = user.uid;
        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
        const usageDocId = `${userId}_${currentMonth}`;
        // Get or create usage document
        const usageRef = db.collection('users').doc(userId).collection('usage').doc(currentMonth);
        const usageDoc = await usageRef.get();
        if (usageDoc.exists) {
            // Increment document count
            await usageRef.update({
                documentsCreated: admin.firestore.FieldValue.increment(1),
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
            });
        }
        else {
            // Create new usage document
            await usageRef.set({
                userId: userId,
                monthYear: currentMonth,
                aiUsageCount: 0,
                documentsCreated: 1,
                speechToTextMinutes: 0,
                createdAt: admin.firestore.FieldValue.serverTimestamp(),
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
            });
        }
        // Get updated usage data
        const updatedUsageDoc = await usageRef.get();
        const updatedUsage = updatedUsageDoc.data();
        // Get user's subscription to determine limits
        const subscriptionDoc = await db.collection('users').doc(userId).collection('subscriptions').doc('current').get();
        const subscription = subscriptionDoc.data();
        const planType = (subscription === null || subscription === void 0 ? void 0 : subscription.planType) || 'free';
        const documentLimit = planType === 'pro' ? 'unlimited' : 10;
        const remaining = documentLimit === 'unlimited' ? 'unlimited' : Math.max(0, documentLimit - ((updatedUsage === null || updatedUsage === void 0 ? void 0 : updatedUsage.documentsCreated) || 0));
        res.json({
            success: true,
            remaining: remaining,
            usage: (updatedUsage === null || updatedUsage === void 0 ? void 0 : updatedUsage.documentsCreated) || 0,
            limit: documentLimit
        });
    }
    catch (error) {
        console.error('Error tracking document creation:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};
exports.trackDocumentCreation = trackDocumentCreation;
//# sourceMappingURL=usage.js.map