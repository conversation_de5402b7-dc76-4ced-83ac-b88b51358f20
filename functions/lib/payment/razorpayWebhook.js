"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.razorpayWebhookHandler = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
const crypto_1 = __importDefault(require("crypto"));
const db = admin.firestore();
const getRazorpayWebhookSecret = () => {
    var _a;
    try {
        return ((_a = functions.config().razorpay) === null || _a === void 0 ? void 0 : _a.webhook_secret) || process.env.RAZORPAY_WEBHOOK_SECRET || '';
    }
    catch (_b) {
        return process.env.RAZORPAY_WEBHOOK_SECRET || '';
    }
};
const WEBHOOK_SECRET = getRazorpayWebhookSecret();
const razorpayWebhookHandler = async (req, res) => {
    var _a, _b, _c, _d, _e, _f;
    try {
        if (!WEBHOOK_SECRET) {
            console.error('Razorpay webhook secret not configured');
            return res.status(500).send('Razorpay is not configured');
        }
        const signature = req.headers['x-razorpay-signature'];
        const body = req.body; // raw body
        const expectedSignature = crypto_1.default
            .createHmac('sha256', WEBHOOK_SECRET)
            .update(body)
            .digest('hex');
        if (signature !== expectedSignature) {
            console.error('Invalid Razorpay webhook signature');
            return res.status(400).send('Invalid signature');
        }
        const payload = JSON.parse(body.toString('utf8'));
        const event = payload.event;
        const data = payload.payload;
        switch (event) {
            case 'subscription.activated':
            case 'subscription.charged': {
                const sub = (_a = data.subscription) === null || _a === void 0 ? void 0 : _a.entity;
                const firebaseUserId = (_b = sub === null || sub === void 0 ? void 0 : sub.notes) === null || _b === void 0 ? void 0 : _b.firebaseUserId;
                if (firebaseUserId) {
                    await db.collection('users').doc(firebaseUserId).collection('subscriptions').doc('current').set({
                        id: 'current',
                        userId: firebaseUserId,
                        planType: 'pro',
                        status: 'active',
                        razorpaySubscriptionId: sub.id,
                        currentPeriodStart: admin.firestore.Timestamp.fromMillis(sub.current_start * 1000),
                        currentPeriodEnd: admin.firestore.Timestamp.fromMillis(sub.current_end * 1000),
                        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
                    }, { merge: true });
                }
                break;
            }
            case 'subscription.halted':
            case 'subscription.pending':
            case 'subscription.paused': {
                const sub = (_c = data.subscription) === null || _c === void 0 ? void 0 : _c.entity;
                const firebaseUserId = (_d = sub === null || sub === void 0 ? void 0 : sub.notes) === null || _d === void 0 ? void 0 : _d.firebaseUserId;
                if (firebaseUserId) {
                    await db.collection('users').doc(firebaseUserId).collection('subscriptions').doc('current').set({
                        status: 'past_due',
                        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
                    }, { merge: true });
                }
                break;
            }
            case 'subscription.cancelled': {
                const sub = (_e = data.subscription) === null || _e === void 0 ? void 0 : _e.entity;
                const firebaseUserId = (_f = sub === null || sub === void 0 ? void 0 : sub.notes) === null || _f === void 0 ? void 0 : _f.firebaseUserId;
                if (firebaseUserId) {
                    await db.collection('users').doc(firebaseUserId).collection('subscriptions').doc('current').set({
                        planType: 'free',
                        status: 'cancelled',
                        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
                    }, { merge: true });
                }
                break;
            }
            default:
                console.log('Unhandled Razorpay event:', event);
        }
        res.json({ received: true });
    }
    catch (error) {
        console.error('Error handling Razorpay webhook:', error);
        res.status(500).json({ error: 'Webhook handler failed' });
    }
};
exports.razorpayWebhookHandler = razorpayWebhookHandler;
//# sourceMappingURL=razorpayWebhook.js.map