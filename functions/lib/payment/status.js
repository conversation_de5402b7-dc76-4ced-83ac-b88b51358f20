"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSubscriptionStatus = void 0;
const admin = __importStar(require("firebase-admin"));
const auth_1 = require("../utils/auth");
const db = admin.firestore();
const getSubscriptionStatus = async (req, res) => {
    try {
        // Verify authentication
        const user = await (0, auth_1.verifyAuthToken)(req);
        if (!user) {
            return res.status(401).json({ error: 'Unauthorized' });
        }
        const userId = req.params.userId;
        // Verify user can only access their own subscription
        if (user.uid !== userId) {
            return res.status(403).json({ error: 'Forbidden' });
        }
        // Get user subscription (nested under users/{uid}/subscriptions/current)
        const subscriptionDoc = await db.collection('users').doc(userId).collection('subscriptions').doc('current').get();
        const subscriptionData = subscriptionDoc.exists ? subscriptionDoc.data() : null;
        // Get current month usage
        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
        const usageDoc = await db.collection('users').doc(userId).collection('usage').doc(currentMonth).get();
        const usageData = usageDoc.exists ? usageDoc.data() : null;
        // Determine plan limits
        const planType = (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.planType) || 'free';
        const limits = planType === 'pro' ? {
            aiUsageLimit: 500,
            documentsLimit: 'unlimited',
            speechToTextLimit: 'unlimited'
        } : {
            aiUsageLimit: 10,
            documentsLimit: 10,
            speechToTextLimit: 60 // minutes
        };
        res.json({
            subscription: subscriptionData || {
                userId: userId,
                planType: 'free',
                status: 'active',
                createdAt: new Date(),
                updatedAt: new Date()
            },
            usage: usageData || {
                userId: userId,
                monthYear: currentMonth,
                aiUsageCount: 0,
                documentsCreated: 0,
                speechToTextMinutes: 0
            },
            limits
        });
    }
    catch (error) {
        console.error('Error getting subscription status:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};
exports.getSubscriptionStatus = getSubscriptionStatus;
//# sourceMappingURL=status.js.map