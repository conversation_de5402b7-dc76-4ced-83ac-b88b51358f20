"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.razorpayWebhook = exports.api = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
// Initialize Firebase Admin
admin.initializeApp();
// CORS configuration
const corsOptions = {
    origin: [
        'http://localhost:4200',
        'https://ai-editor-3901f.web.app',
        'https://ai-editor-3901f.firebaseapp.com'
    ],
    credentials: true
};
// Express app for API routes
const app = (0, express_1.default)();
app.use((0, cors_1.default)(corsOptions));
app.use(express_1.default.json());
// Import route handlers
// Stripe handlers removed; using Razorpay instead
const razorpay_1 = require("./payment/razorpay");
const status_1 = require("./payment/status");
const usage_1 = require("./payment/usage");
// Payment routes (Razorpay)
app.post('/razorpay/create-subscription', razorpay_1.createSubscription);
app.post('/razorpay/verify-payment', razorpay_1.verifyPayment);
app.post('/razorpay/cancel-subscription', razorpay_1.cancelRazorpaySubscription);
// One-time order flow (tutorial style)
app.post('/razorpay/create-order', razorpay_1.createOrder);
app.post('/razorpay/verify-order', razorpay_1.verifyOrder);
app.get('/subscription-status/:userId', status_1.getSubscriptionStatus);
// Usage tracking routes
app.post('/track-ai-usage', usage_1.trackAIUsage);
app.post('/track-document-creation', usage_1.trackDocumentCreation);
// Razorpay webhook endpoint (needs raw body)
const razorpayWebhook_1 = require("./payment/razorpayWebhook");
const webhookRazorpayApp = (0, express_1.default)();
webhookRazorpayApp.use(express_1.default.raw({ type: 'application/json' }));
webhookRazorpayApp.post('/', razorpayWebhook_1.razorpayWebhookHandler);
// Export Cloud Functions
exports.api = functions.https.onRequest(app);
exports.razorpayWebhook = functions.https.onRequest(webhookRazorpayApp);
//# sourceMappingURL=index.js.map